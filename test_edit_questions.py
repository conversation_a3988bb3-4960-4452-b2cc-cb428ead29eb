#!/usr/bin/env python3
"""
Test script to verify edit questions functionality for all teachers
"""

from db import get_teacher_subjects, get_questions_by_subject_and_teacher

def test_teacher_edit_access():
    """Test that all teachers can access subjects and questions for editing"""
    
    print("🧪 Testing Edit Questions Access for All Teachers")
    print("=" * 60)
    
    teachers = ["teacher1", "teacher2", "teacher3"]
    
    for teacher_id in teachers:
        print(f"\n👤 Testing {teacher_id.upper()}:")
        print("-" * 30)
        
        # Test subject access
        subjects = get_teacher_subjects(teacher_id)
        print(f"📚 Subjects available: {len(subjects)}")
        
        if subjects:
            for subject in subjects[:3]:  # Show first 3 subjects
                print(f"   • {subject}")
                
                # Test question access for this subject
                questions = get_questions_by_subject_and_teacher(subject, teacher_id)
                print(f"     📝 Questions: {len(questions)}")
                
                if questions:
                    # Show first question as example
                    first_q = questions[0]
                    question_text = first_q.get('question', 'No question text')[:50]
                    print(f"     📄 Example: {question_text}...")
                    print(f"     🆔 Question ID: {first_q.get('id', 'No ID')}")
                else:
                    print(f"     ⚠️ No questions found for {subject}")
            
            if len(subjects) > 3:
                print(f"   ... and {len(subjects) - 3} more subjects")
        else:
            print("   ❌ No subjects found")
    
    print("\n" + "=" * 60)
    print("✅ Test completed!")
    
    # Summary
    all_subjects = get_teacher_subjects("teacher1")
    print(f"\n📊 Summary:")
    print(f"   • Total subjects in system: {len(all_subjects)}")
    print(f"   • All teachers should see the same subjects for editing")
    print(f"   • This allows collaborative editing while tracking creation")

if __name__ == "__main__":
    try:
        test_teacher_edit_access()
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("\n🔧 Make sure you've added teacher_id columns to the database:")
        print("   python add_teacher_columns.py")
