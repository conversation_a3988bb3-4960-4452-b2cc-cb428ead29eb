#!/usr/bin/env python3
"""
Robust startup script for the Teacher Bo<PERSON> with network error handling
"""

import sys
import time
import subprocess
import signal
import os

def check_internet_connection():
    """Check if internet connection is available"""
    try:
        import requests
        response = requests.get("https://api.telegram.org", timeout=10)
        return True
    except:
        return False

def start_bot():
    """Start the teacher bot with error handling"""
    print("🤖 Starting Multi-Teacher Quiz Bot System")
    print("=" * 50)
    
    # Check internet connection
    print("🌐 Checking internet connection...")
    if not check_internet_connection():
        print("❌ No internet connection detected")
        print("🔧 Please check your network connection and try again")
        return False
    
    print("✅ Internet connection OK")
    
    # Check if bot file exists
    if not os.path.exists("TelegramBot.py"):
        print("❌ TelegramBot.py not found")
        print("🔧 Please ensure you're in the correct directory")
        return False
    
    print("✅ Bot file found")
    
    # Display teacher credentials
    print("\n🔑 Teacher Login Credentials:")
    print("-" * 30)
    print("Teacher 1: &e^!tK$mVKwLyGjQXq6RTUsJ#@X$n@ycvjU6E!a^EDLfslFx$A")
    print("Teacher 2: yd2gI4&h3Qs3USd8n63@zsGbI!xA3#tWVnIn2Fq9xouLRkHnch")
    print("Teacher 3: 5!MM8MA!BLcqDaPC3%4rgB7kPsxF376HJ$6ncl43#!EMqE6*zx")
    print("-" * 30)
    
    print("\n🚀 Starting bot...")
    print("📋 Features enabled:")
    print("   • Multi-teacher authentication")
    print("   • Data isolation between teachers")
    print("   • Network timeout protection")
    print("   • Automatic retry on connection errors")
    print("   • Anti-cheat protection for students")
    
    print("\n⚠️ Network Error Handling:")
    print("   • Automatic reconnection on timeouts")
    print("   • Exponential backoff for retries")
    print("   • Graceful error recovery")
    
    print("\nPress Ctrl+C to stop the bot...")
    print("=" * 50)
    
    process = None
    try:
        # Start the bot process
        process = subprocess.Popen([sys.executable, "TelegramBot.py"])

        # Wait for the process to complete
        process.wait()
        return True

    except KeyboardInterrupt:
        print("\n🛑 Stopping Teacher Bot...")
        if process:
            try:
                process.terminate()
                process.wait(timeout=5)
                print("✅ Teacher Bot stopped successfully")
            except subprocess.TimeoutExpired:
                print("⚠️ Force killing Teacher Bot...")
                process.kill()
                process.wait()
                print("✅ Teacher Bot force stopped")
        return True

    except Exception as e:
        print(f"❌ Error starting bot: {e}")
        if process:
            process.terminate()
        return False

def main():
    """Main function with restart logic"""
    restart_count = 0
    max_restarts = 5
    
    while restart_count < max_restarts:
        if start_bot():
            # Bot stopped normally
            break
        else:
            # Bot crashed, try to restart
            restart_count += 1
            if restart_count < max_restarts:
                print(f"\n🔄 Restarting bot (attempt {restart_count}/{max_restarts})...")
                time.sleep(5)
            else:
                print(f"\n❌ Bot failed to start after {max_restarts} attempts")
                print("🔧 Please check the error messages above and fix any issues")
                return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Startup script stopped by user")
        sys.exit(0)
