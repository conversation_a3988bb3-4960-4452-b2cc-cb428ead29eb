#!/usr/bin/env python3
"""
Script to check for and resolve bot conflicts
"""

import psutil
import sys
import time
import os

def find_python_processes():
    """Find all Python processes that might be running bots"""
    python_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and len(cmdline) > 1:
                    script_name = cmdline[1] if len(cmdline) > 1 else ''
                    if any(bot_file in script_name for bot_file in ['TelegramBot.py', 'StudentBot.py', 'run_bots.py']):
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'script': script_name,
                            'cmdline': ' '.join(cmdline)
                        })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    return python_processes

def kill_bot_processes():
    """Kill all bot-related processes"""
    processes = find_python_processes()
    
    if not processes:
        print("✅ No bot processes found running")
        return True
    
    print(f"🔍 Found {len(processes)} bot processes:")
    for proc in processes:
        print(f"   PID {proc['pid']}: {proc['script']}")
    
    print("\n🛑 Stopping all bot processes...")
    
    killed_count = 0
    for proc in processes:
        try:
            process = psutil.Process(proc['pid'])
            process.terminate()
            
            # Wait for graceful termination
            try:
                process.wait(timeout=5)
                print(f"✅ Stopped PID {proc['pid']}: {proc['script']}")
                killed_count += 1
            except psutil.TimeoutExpired:
                # Force kill if needed
                process.kill()
                process.wait()
                print(f"⚠️ Force killed PID {proc['pid']}: {proc['script']}")
                killed_count += 1
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"❌ Could not stop PID {proc['pid']}: {e}")
    
    print(f"\n✅ Stopped {killed_count} processes")
    return killed_count > 0

def check_telegram_connection():
    """Test if we can connect to Telegram without conflicts"""
    print("🔍 Testing Telegram connection...")
    
    try:
        import requests
        
        # Test basic connectivity
        response = requests.get("https://api.telegram.org", timeout=10)
        if response.status_code == 200:
            print("✅ Telegram API is reachable")
            return True
        else:
            print(f"⚠️ Telegram API returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Cannot reach Telegram API: {e}")
        return False

def wait_for_telegram_clear():
    """Wait for Telegram to clear previous connections"""
    print("⏳ Waiting for Telegram to clear previous connections...")
    
    for i in range(30, 0, -1):
        print(f"\r⏰ Waiting {i} seconds...", end='', flush=True)
        time.sleep(1)
    
    print("\n✅ Wait complete")

def main():
    """Main conflict resolution function"""
    print("🔍 Bot Conflict Detection and Resolution")
    print("=" * 50)
    
    # Step 1: Find and kill existing processes
    print("Step 1: Checking for running bot processes...")
    processes_killed = kill_bot_processes()
    
    # Step 2: Wait if we killed processes
    if processes_killed:
        wait_for_telegram_clear()
    
    # Step 3: Test Telegram connectivity
    print("\nStep 2: Testing Telegram connectivity...")
    if not check_telegram_connection():
        print("❌ Cannot connect to Telegram. Check your internet connection.")
        return 1
    
    # Step 4: Final check
    print("\nStep 3: Final process check...")
    final_processes = find_python_processes()
    
    if final_processes:
        print("⚠️ Some bot processes are still running:")
        for proc in final_processes:
            print(f"   PID {proc['pid']}: {proc['script']}")
        print("\n🔧 You may need to:")
        print("   • Restart your computer")
        print("   • Manually kill processes in Task Manager")
        return 1
    
    print("✅ No conflicts detected")
    print("\n🚀 Safe to start bots now!")
    print("\nRecommended startup:")
    print("   python start_both_bots.py")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Conflict check cancelled")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error during conflict check: {e}")
        sys.exit(1)
