#!/usr/bin/env python3
"""
Script to run both Teacher <PERSON> and <PERSON> simultaneously
"""

import threading
import time
import sys
import subprocess
import signal
import os

# Global variables to track processes
teacher_process = None
student_process = None

def signal_handler(signum, frame):
    """Handle Ctrl+C signal"""
    print("\n🛑 Stopping both bots...")

    global teacher_process, student_process

    # Stop Teacher Bot
    if teacher_process and teacher_process.poll() is None:
        print("🎓 Stopping Teacher Bot...")
        try:
            teacher_process.terminate()
            teacher_process.wait(timeout=5)
            print("✅ Teacher Bot stopped")
        except subprocess.TimeoutExpired:
            print("⚠️ Force killing Teacher Bot...")
            teacher_process.kill()
            teacher_process.wait()
            print("✅ Teacher Bot force stopped")

    # Stop Student Bot
    if student_process and student_process.poll() is None:
        print("🎒 Stopping Student Bot...")
        try:
            student_process.terminate()
            student_process.wait(timeout=5)
            print("✅ Student Bot stopped")
        except subprocess.TimeoutExpired:
            print("⚠️ Force killing Student Bot...")
            student_process.kill()
            student_process.wait()
            print("✅ Student Bot force stopped")

    print("👋 All bots stopped successfully!")
    sys.exit(0)

def run_teacher_bot():
    """Run the teacher bot as subprocess"""
    global teacher_process
    try:
        print("🎓 Starting Teacher Bot...")
        teacher_process = subprocess.Popen([sys.executable, "TelegramBot.py"])
        teacher_process.wait()
        print("✅ Teacher Bot finished")
    except Exception as e:
        print(f"❌ Error starting Teacher Bot: {e}")

def run_student_bot():
    """Run the student bot as subprocess"""
    global student_process
    try:
        print("🎒 Starting Student Bot...")
        student_process = subprocess.Popen([sys.executable, "StudentBot.py"])
        student_process.wait()
        print("✅ Student Bot finished")
    except Exception as e:
        print(f"❌ Error starting Student Bot: {e}")

def main():
    # Register signal handler for Ctrl+C
    signal.signal(signal.SIGINT, signal_handler)

    print("🤖 Starting Quiz Bot System...")
    print("=" * 50)
    print("🔐 Multi-teacher authentication enabled")
    print("🛡️ Anti-cheat protection enabled")
    print("⚠️ Network timeout protection enabled")
    print("🛑 Proper shutdown handling enabled")
    print("\n🔑 Teacher Login Credentials:")
    print("Teacher 1: &e^!tK$mVKwLyGjQXq6RTUsJ#@X$n@ycvjU6E!a^EDLfslFx$A")
    print("Teacher 2: yd2gI4&h3Qs3USd8n63@zsGbI!xA3#tWVnIn2Fq9xouLRkHnch")
    print("Teacher 3: 5!MM8MA!BLcqDaPC3%4rgB7kPsxF376HJ$6ncl43#!EMqE6*zx")
    print("=" * 50)

    # Create threads for both bots
    teacher_thread = threading.Thread(target=run_teacher_bot, daemon=False)
    student_thread = threading.Thread(target=run_student_bot, daemon=False)

    # Start both bots
    teacher_thread.start()
    time.sleep(2)  # Small delay between starting bots
    student_thread.start()

    print("\n🚀 Both bots are running!")
    print("📋 Teacher Bot: Multi-teacher authentication system")
    print("🎓 Student Bot (@TestStudentCollegeBot): Anti-cheat quiz system")
    print("🔄 Network error handling: Automatic retry on timeouts")
    print("🛑 Press Ctrl+C to stop both bots properly...")

    try:
        # Wait for both threads to complete
        teacher_thread.join()
        student_thread.join()
    except KeyboardInterrupt:
        # This should be handled by signal_handler
        pass

if __name__ == "__main__":
    main()
