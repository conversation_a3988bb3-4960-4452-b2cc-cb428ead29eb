#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run both Teacher <PERSON> and <PERSON> <PERSON> simultaneously
"""

import threading
import time
import sys

def run_teacher_bot():
    """Run the teacher bot"""
    try:
        print("🎓 Starting Teacher Bot...")
        import TelegramBot
        print("✅ Teacher Bot started successfully!")
    except Exception as e:
        print(f"❌ Error starting Teacher Bot: {e}")

def run_student_bot():
    """Run the student bot"""
    try:
        print("🎒 Starting Student Bot...")
        import StudentBot
        print("✅ Student Bot started successfully!")
    except Exception as e:
        print(f"❌ Error starting Student Bot: {e}")

def main():
    print("🤖 Starting Quiz Bot System...")
    print("=" * 50)
    print("🔐 Multi-teacher authentication enabled")
    print("🛡️ Anti-cheat protection enabled")
    print("⚠️ Network timeout protection enabled")
    print("\n🔑 Teacher Login Credentials:")
    print("Teacher 1: &e^!tK$mVKwLyGjQXq6RTUsJ#@X$n@ycvjU6E!a^EDLfslFx$A")
    print("Teacher 2: yd2gI4&h3Qs3USd8n63@zsGbI!xA3#tWVnIn2Fq9xouLRkHnch")
    print("Teacher 3: 5!MM8MA!BLcqDaPC3%4rgB7kPsxF376HJ$6ncl43#!EMqE6*zx")
    print("=" * 50)

    # Create threads for both bots
    teacher_thread = threading.Thread(target=run_teacher_bot, daemon=True)
    student_thread = threading.Thread(target=run_student_bot, daemon=True)

    # Start both bots
    teacher_thread.start()
    time.sleep(2)  # Small delay between starting bots
    student_thread.start()

    print("\n🚀 Both bots are running!")
    print("📋 Teacher Bot: Multi-teacher authentication system")
    print("🎓 Student Bot (@TestStudentCollegeBot): Anti-cheat quiz system")
    print("🔄 Network error handling: Automatic retry on timeouts")
    print("\nPress Ctrl+C to stop both bots...")

    try:
        # Keep the main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping bots...")
        print("👋 Goodbye!")
        sys.exit(0)

if __name__ == "__main__":
    main()
