# db.py file 
from supabase import create_client
import uuid

# Supabase credentials
SUPABASE_URL = 'https://onvhyjlazgptfxhdlmrj.supabase.co'
SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9udmh5amxhemdwdGZ4aGRsbXJqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0OTQ0NTIsImV4cCI6MjA2NzA3MDQ1Mn0.Jy59fTbV5weHj4ySc4kubl0WbdMhQc-HueMXNMxY6Is'

# Connect to Supabase
supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

def load_questions():
    response = supabase.table("questions").select("*").order("id").execute()
    return response.data

def save_question(question):
    supabase.table("questions").insert(question).execute()

def save_batch_link(batch_id, link_token, subject, number_of_questions, duration_minutes):
    telegram_link = f"https://t.me/TestStudentCollegeBot?start=quiz_{link_token}"

    # Upsert batch metadata
    supabase.table("batches_metadata").upsert({
        "batch_id": batch_id,
        "subject": subject,
        "number_of_questions": number_of_questions,
        "duration_minutes": duration_minutes
    }).execute()

    # Upsert batch link info (use upsert to prevent duplicate key errors)
    supabase.table("batch_links").upsert({
        "batch_id": batch_id,
        "link_token": link_token,
        "telegram_link": telegram_link,
        "used": False
    }).execute()

def is_link_used(link_token):
    response = supabase.table("batch_links").select("used").eq("link_token", link_token).execute()
    if not response.data:
        return True
    return response.data[0]["used"]

def mark_link_used(link_token):
    # Mark batch_links as used
    supabase.table("batch_links").update({"used": True}).eq("link_token", link_token).execute()
    
    # Also mark all questions in the batch as used
    resp = supabase.table("batch_links").select("batch_id").eq("link_token", link_token).execute()
    if resp.data:
        batch_id = resp.data[0]["batch_id"]
        supabase.table("questions").update({"used": True}).eq("batch_id", batch_id).execute()

def load_questions_by_batch(link_token):
    response = supabase.table("batch_links").select("batch_id").eq("link_token", link_token).execute()
    if not response.data:
        return None
    batch_id = response.data[0]["batch_id"]
    response = supabase.table("questions").select("*").eq("batch_id", batch_id).execute()
    if not response.data:
        return None
    return response.data

def get_telegram_link(link_token):
    response = supabase.table("batch_links").select("telegram_link").eq("link_token", link_token).execute()
    if not response.data:
        return None
    return response.data[0]["telegram_link"]

def get_batch_metadata(link_token):
    """Get batch metadata including duration for a specific link token"""
    # First get batch_id from link_token
    response = supabase.table("batch_links").select("batch_id").eq("link_token", link_token).execute()
    if not response.data:
        return None

    batch_id = response.data[0]["batch_id"]

    # Get metadata for this batch
    meta_resp = supabase.table("batches_metadata").select("*").eq("batch_id", batch_id).execute()
    if not meta_resp.data:
        return None

    return meta_resp.data[0]

def get_all_batches():
    meta_resp = supabase.table("batches_metadata").select("*").execute()
    if not meta_resp.data:
        return []

    metadata = meta_resp.data
    links_resp = supabase.table("batch_links").select("*").execute()
    if not links_resp.data:
        return []

    links = links_resp.data
    links_map = {l["batch_id"]: l for l in links}

    batches = []
    for batch in metadata:
        batch_id = batch["batch_id"]
        link_info = links_map.get(batch_id, {})
        batches.append({
            "batch_id": batch_id,
            "subject": batch.get("subject", ""),
            "question_count": batch.get("number_of_questions", 0),
            "time": batch.get("duration_minutes", 15),
            "telegram_link": link_info.get("telegram_link", ""),
            "used": link_info.get("used", False)
        })
    return batches
