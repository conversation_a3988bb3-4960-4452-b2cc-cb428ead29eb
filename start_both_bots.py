#!/usr/bin/env python3
"""
Simple script to start both bots with proper Ctrl+C handling
"""

import subprocess
import sys
import signal
import time
import os

# Global variables to track processes
teacher_process = None
student_process = None

def cleanup_processes():
    """Clean up both bot processes"""
    global teacher_process, student_process
    
    print("\n🛑 Stopping both bots...")
    
    # Stop Teacher Bot
    if teacher_process and teacher_process.poll() is None:
        print("🎓 Stopping Teacher Bot...")
        try:
            teacher_process.terminate()
            teacher_process.wait(timeout=5)
            print("✅ Teacher Bot stopped")
        except subprocess.TimeoutExpired:
            print("⚠️ Force killing Teacher Bot...")
            teacher_process.kill()
            teacher_process.wait()
            print("✅ Teacher Bot force stopped")
        except Exception as e:
            print(f"⚠️ Error stopping Teacher Bot: {e}")
    
    # Stop Student Bot
    if student_process and student_process.poll() is None:
        print("🎒 Stopping Student Bot...")
        try:
            student_process.terminate()
            student_process.wait(timeout=5)
            print("✅ Student Bot stopped")
        except subprocess.TimeoutExpired:
            print("⚠️ Force killing Student Bot...")
            student_process.kill()
            student_process.wait()
            print("✅ Student Bot force stopped")
        except Exception as e:
            print(f"⚠️ Error stopping Student Bot: {e}")

def signal_handler(signum, frame):
    """Handle Ctrl+C signal"""
    cleanup_processes()
    print("👋 All bots stopped successfully!")
    sys.exit(0)

def check_for_conflicts():
    """Check for existing bot processes"""
    try:
        import psutil

        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline and len(cmdline) > 1:
                        script_name = cmdline[1] if len(cmdline) > 1 else ''
                        if any(bot_file in script_name for bot_file in ['TelegramBot.py', 'StudentBot.py']):
                            python_processes.append({
                                'pid': proc.info['pid'],
                                'script': os.path.basename(script_name)
                            })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass

        if python_processes:
            print("⚠️ Found existing bot processes:")
            for proc in python_processes:
                print(f"   PID {proc['pid']}: {proc['script']}")
            print("\n🔧 Please run this first to stop conflicts:")
            print("   python check_bot_conflicts.py")
            return False

        return True

    except ImportError:
        print("⚠️ Cannot check for conflicts (psutil not installed)")
        print("🔧 Install with: pip install psutil")
        return True  # Continue anyway

def main():
    global teacher_process, student_process

    # Register signal handler for Ctrl+C
    signal.signal(signal.SIGINT, signal_handler)

    print("🤖 Starting Quiz Bot System...")
    print("=" * 50)
    print("🔐 Multi-teacher authentication enabled")
    print("🛡️ Anti-cheat protection enabled")
    print("⚠️ Network timeout protection enabled")
    print("🛑 Proper shutdown handling enabled")
    print("🔍 Conflict detection enabled")

    # Check for conflicts first
    print("\n🔍 Checking for existing bot processes...")
    if not check_for_conflicts():
        print("\n❌ Cannot start - conflicts detected")
        return 1
    print("✅ No conflicts detected")

    print("\n🔑 Teacher Login Credentials:")
    print("Teacher 1: &e^!tK$mVKwLyGjQXq6RTUsJ#@X$n@ycvjU6E!a^EDLfslFx$A")
    print("Teacher 2: yd2gI4&h3Qs3USd8n63@zsGbI!xA3#tWVnIn2Fq9xouLRkHnch")
    print("Teacher 3: 5!MM8MA!BLcqDaPC3%4rgB7kPsxF376HJ$6ncl43#!EMqE6*zx")
    print("=" * 50)
    
    try:
        # Start Teacher Bot
        print("\n🎓 Starting Teacher Bot...")
        teacher_process = subprocess.Popen(
            [sys.executable, "TelegramBot.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        # Wait a moment
        time.sleep(2)
        
        # Start Student Bot
        print("🎒 Starting Student Bot...")
        student_process = subprocess.Popen(
            [sys.executable, "StudentBot.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        print("\n🚀 Both bots are running!")
        print("📋 Teacher Bot: Multi-teacher authentication system")
        print("🎓 Student Bot (@TestStudentCollegeBot): Anti-cheat quiz system")
        print("🔄 Network error handling: Automatic retry on timeouts")
        print("\n🛑 Press Ctrl+C to stop both bots properly...")
        print("📊 Bot status will be monitored...")
        
        # Monitor both processes
        while True:
            # Check if Teacher Bot is still running
            if teacher_process.poll() is not None:
                print("❌ Teacher Bot stopped unexpectedly")
                break
            
            # Check if Student Bot is still running
            if student_process.poll() is not None:
                print("❌ Student Bot stopped unexpectedly")
                break
            
            # Wait a bit before checking again
            time.sleep(1)
            
    except KeyboardInterrupt:
        # This should be handled by signal_handler
        pass
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        cleanup_processes()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
