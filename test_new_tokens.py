#!/usr/bin/env python3
"""
Test script to verify new bot tokens work without conflicts
"""

import requests
import time

# New bot tokens
TEACHER_TOKEN = "7952963406:AAHAVoYJtyyNrbTQtfP-pd9FYV9_O6aUWr0"
STUDENT_TOKEN = "7736646386:AAFQlNzSuhFDDj2WqQ5R8PaVVJau99d-QVg"

def test_bot_token(token, bot_name):
    """Test if a bot token works without conflicts"""
    print(f"\n🧪 Testing {bot_name}...")
    
    try:
        # Test 1: Get bot info
        response = requests.get(f"https://api.telegram.org/bot{token}/getMe", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data['ok']:
                bot_info = data['result']
                print(f"✅ Bot Info: @{bot_info['username']} ({bot_info['first_name']})")
            else:
                print(f"❌ Bot API error: {data.get('description', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
        
        # Test 2: Check webhook status
        webhook_response = requests.get(f"https://api.telegram.org/bot{token}/getWebhookInfo", timeout=10)
        if webhook_response.status_code == 200:
            webhook_data = webhook_response.json()
            if webhook_data['ok']:
                webhook_info = webhook_data['result']
                if webhook_info.get('url'):
                    print(f"⚠️ Webhook set: {webhook_info['url']}")
                else:
                    print("✅ No webhook (good for polling)")
            else:
                print(f"⚠️ Webhook check failed: {webhook_data.get('description')}")
        
        # Test 3: Try polling (this is what causes Error 409)
        print("🔍 Testing polling capability...")
        poll_response = requests.post(
            f"https://api.telegram.org/bot{token}/getUpdates",
            json={"timeout": 1, "limit": 1},
            timeout=5
        )
        
        if poll_response.status_code == 200:
            poll_data = poll_response.json()
            if poll_data['ok']:
                print("✅ Polling works - no conflicts!")
                return True
            else:
                error_desc = poll_data.get('description', 'Unknown error')
                if 'conflict' in error_desc.lower():
                    print(f"❌ Conflict detected: {error_desc}")
                    return False
                else:
                    print(f"⚠️ Polling error: {error_desc}")
                    return False
        else:
            print(f"❌ Polling HTTP error: {poll_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def main():
    """Test both new bot tokens"""
    print("🚀 NEW BOT TOKENS TEST")
    print("=" * 60)
    print("Testing if new tokens resolve Error 409 conflicts")
    print("=" * 60)
    
    # Test teacher bot
    teacher_ok = test_bot_token(TEACHER_TOKEN, "Teacher Bot (@QuizTeacherBotV2_bot)")
    
    # Wait a moment between tests
    time.sleep(2)
    
    # Test student bot
    student_ok = test_bot_token(STUDENT_TOKEN, "Student Bot (@QuizeStudentBotV2_bot)")
    
    # Results
    print("\n" + "=" * 60)
    if teacher_ok and student_ok:
        print("🎉 SUCCESS! Both new tokens work perfectly")
        print("\n✅ No Error 409 conflicts detected")
        print("✅ Both bots ready for polling")
        print("✅ No webhooks interfering")
        
        print("\n🚀 Ready to start bots:")
        print("   Terminal 1: python TelegramBot.py")
        print("   Terminal 2: python StudentBot.py")
        
        print("\n📋 New Bot Information:")
        print("   • Teacher Bot: @QuizTeacherBotV2_bot")
        print("   • Student Bot: @QuizeStudentBotV2_bot")
        print("   • Both tokens are fresh and conflict-free")
        
        return 0
    else:
        print("❌ Some issues detected")
        if not teacher_ok:
            print("   • Teacher bot has issues")
        if not student_ok:
            print("   • Student bot has issues")
        
        print("\n🔧 If issues persist:")
        print("   1. Wait 5 minutes and try again")
        print("   2. Check internet connection")
        print("   3. Verify tokens are correct")
        
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Test cancelled")
        exit(0)
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        exit(1)
