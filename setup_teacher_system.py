#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to set up the multi-teacher authentication system:
1. Add teacher_id column to existing tables
2. Assign existing data to teacher1 as default
3. Test the authentication system
"""

from db import supabase, TEACHER_PASSWORDS, authenticate_teacher

def add_teacher_id_columns():
    """Add teacher_id columns to existing tables"""
    print("🔧 Adding teacher_id columns to database tables...")
    
    try:
        # Add teacher_id to batches_metadata table
        print("📋 Adding teacher_id to batches_metadata...")
        supabase.rpc('add_column_if_not_exists', {
            'table_name': 'batches_metadata',
            'column_name': 'teacher_id',
            'column_type': 'text'
        }).execute()
        
        # Add teacher_id to questions table
        print("📝 Adding teacher_id to questions...")
        supabase.rpc('add_column_if_not_exists', {
            'table_name': 'questions', 
            'column_name': 'teacher_id',
            'column_type': 'text'
        }).execute()
        
        print("✅ Teacher ID columns added successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error adding columns: {e}")
        print("ℹ️ This might be normal if columns already exist")
        return True  # Continue anyway

def assign_existing_data_to_teacher1():
    """Assign all existing data to teacher1 as default"""
    print("\n🔧 Assigning existing data to teacher1...")
    
    try:
        # Update batches_metadata
        result1 = supabase.table("batches_metadata").update({
            "teacher_id": "teacher1"
        }).is_("teacher_id", "null").execute()
        
        updated_batches = len(result1.data) if result1.data else 0
        print(f"📋 Updated {updated_batches} batches for teacher1")
        
        # Update questions
        result2 = supabase.table("questions").update({
            "teacher_id": "teacher1"
        }).is_("teacher_id", "null").execute()
        
        updated_questions = len(result2.data) if result2.data else 0
        print(f"📝 Updated {updated_questions} questions for teacher1")
        
        print("✅ Existing data assigned to teacher1")
        return True
        
    except Exception as e:
        print(f"❌ Error assigning data: {e}")
        return False

def test_authentication_system():
    """Test the authentication system"""
    print("\n🧪 Testing authentication system...")
    
    # Test each teacher password
    for i, (teacher_id, password) in enumerate(TEACHER_PASSWORDS.items(), 1):
        result = authenticate_teacher(password)
        if result == teacher_id:
            print(f"✅ Teacher {i} authentication: PASS")
        else:
            print(f"❌ Teacher {i} authentication: FAIL")
            return False
    
    # Test invalid password
    invalid_result = authenticate_teacher("invalid_password")
    if invalid_result is None:
        print("✅ Invalid password rejection: PASS")
    else:
        print("❌ Invalid password rejection: FAIL")
        return False
    
    print("✅ All authentication tests passed")
    return True

def show_teacher_credentials():
    """Display teacher credentials for reference"""
    print("\n🔑 Teacher Credentials:")
    print("=" * 60)
    
    for i, (teacher_id, password) in enumerate(TEACHER_PASSWORDS.items(), 1):
        print(f"Teacher {i} ({teacher_id}):")
        print(f"Password: {password}")
        print()

def show_database_status():
    """Show current database status"""
    print("\n📊 Database Status:")
    print("=" * 60)
    
    try:
        # Check batches_metadata
        batches_resp = supabase.table("batches_metadata").select("teacher_id, subject").execute()
        if batches_resp.data:
            print(f"📋 Batches by teacher:")
            teacher_batches = {}
            for batch in batches_resp.data:
                teacher_id = batch.get('teacher_id', 'unassigned')
                if teacher_id not in teacher_batches:
                    teacher_batches[teacher_id] = []
                teacher_batches[teacher_id].append(batch.get('subject', 'Unknown'))
            
            for teacher_id, subjects in teacher_batches.items():
                print(f"  {teacher_id}: {len(subjects)} batches ({', '.join(subjects[:3])}{'...' if len(subjects) > 3 else ''})")
        
        # Check questions
        questions_resp = supabase.table("questions").select("teacher_id").execute()
        if questions_resp.data:
            print(f"\n📝 Questions by teacher:")
            teacher_questions = {}
            for question in questions_resp.data:
                teacher_id = question.get('teacher_id', 'unassigned')
                teacher_questions[teacher_id] = teacher_questions.get(teacher_id, 0) + 1
            
            for teacher_id, count in teacher_questions.items():
                print(f"  {teacher_id}: {count} questions")
        
    except Exception as e:
        print(f"❌ Error checking database status: {e}")

def main():
    """Run all setup steps"""
    print("🚀 Setting up Multi-Teacher Authentication System")
    print("=" * 60)
    
    # Step 1: Add columns
    if not add_teacher_id_columns():
        print("❌ Failed to add columns. Exiting.")
        return 1
    
    # Step 2: Assign existing data
    if not assign_existing_data_to_teacher1():
        print("❌ Failed to assign existing data. Exiting.")
        return 1
    
    # Step 3: Test authentication
    if not test_authentication_system():
        print("❌ Authentication tests failed. Exiting.")
        return 1
    
    # Step 4: Show status
    show_database_status()
    show_teacher_credentials()
    
    print("\n" + "=" * 60)
    print("🎉 Multi-Teacher System Setup Complete!")
    print("\n📋 What's been set up:")
    print("   • Teacher authentication with 3 unique passwords")
    print("   • Separate sessions for each teacher")
    print("   • Data isolation between teachers")
    print("   • Existing data assigned to teacher1")
    print("\n🚀 Teachers can now use:")
    print("   • /login - to authenticate")
    print("   • /logout - to end session")
    print("   • All existing commands (with authentication)")
    print("\n⚠️ Important: Each teacher can only see their own quizzes!")
    
    return 0

if __name__ == "__main__":
    exit(main())
