#!/usr/bin/env python3
"""
<PERSON>ript to update bot tokens in the code files
"""

import re
import sys

def update_teacher_bot_token(new_token):
    """Update teacher bot token in TelegramBot.py"""
    try:
        with open('TelegramBot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace the token line
        pattern = r'TOKEN = "PUT_YOUR_NEW_TEACHER_BOT_TOKEN_HERE"'
        replacement = f'TOKEN = "{new_token}"'
        
        if 'PUT_YOUR_NEW_TEACHER_BOT_TOKEN_HERE' in content:
            content = content.replace('TOKEN = "PUT_YOUR_NEW_TEACHER_BOT_TOKEN_HERE"', f'TOKEN = "{new_token}"')
            
            with open('TelegramBot.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Teacher bot token updated in TelegramBot.py")
            return True
        else:
            print("⚠️ Teacher bot token placeholder not found")
            return False
            
    except Exception as e:
        print(f"❌ Error updating teacher bot token: {e}")
        return False

def update_student_bot_token(new_token):
    """Update student bot token in StudentBot.py"""
    try:
        with open('StudentBot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace the token line
        if 'PUT_YOUR_NEW_STUDENT_BOT_TOKEN_HERE' in content:
            content = content.replace('STUDENT_TOKEN = "PUT_YOUR_NEW_STUDENT_BOT_TOKEN_HERE"', f'STUDENT_TOKEN = "{new_token}"')
            
            with open('StudentBot.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Student bot token updated in StudentBot.py")
            return True
        else:
            print("⚠️ Student bot token placeholder not found")
            return False
            
    except Exception as e:
        print(f"❌ Error updating student bot token: {e}")
        return False

def validate_token(token):
    """Validate bot token format"""
    pattern = r'^\d{8,10}:[A-Za-z0-9_-]{35}$'
    return re.match(pattern, token) is not None

def main():
    """Main function to update bot tokens"""
    print("🔧 Bot Token Updater")
    print("=" * 50)
    print("This will update your bot tokens to resolve Error 409")
    print("=" * 50)
    
    # Get teacher bot token
    print("\n📋 Step 1: Teacher Bot Token")
    teacher_token = input("Enter your NEW teacher bot token from @BotFather: ").strip()
    
    if not validate_token(teacher_token):
        print("❌ Invalid teacher bot token format")
        print("Token should look like: 1234567890:ABCdefGHIjklMNOpqrSTUvwxYZ")
        return 1
    
    # Get student bot token
    print("\n📋 Step 2: Student Bot Token")
    student_token = input("Enter your NEW student bot token from @BotFather: ").strip()
    
    if not validate_token(student_token):
        print("❌ Invalid student bot token format")
        print("Token should look like: 0987654321:ZYXwvuTSRqpONMlkJIhgFEdcBA")
        return 1
    
    # Update tokens
    print("\n🔧 Updating tokens...")
    teacher_success = update_teacher_bot_token(teacher_token)
    student_success = update_student_bot_token(student_token)
    
    if teacher_success and student_success:
        print("\n🎉 SUCCESS! Both tokens updated")
        print("\n🚀 Next steps:")
        print("1. Start teacher bot: python TelegramBot.py")
        print("2. Start student bot: python StudentBot.py")
        print("3. Test both bots - Error 409 should be resolved!")
        return 0
    else:
        print("\n❌ Some tokens failed to update")
        print("Please manually edit the files:")
        print("- TelegramBot.py: Update TOKEN variable")
        print("- StudentBot.py: Update STUDENT_TOKEN variable")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Token update cancelled")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
