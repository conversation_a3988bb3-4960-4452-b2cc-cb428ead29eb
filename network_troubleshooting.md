# 🌐 Network Troubleshooting Guide

## 🚨 Common Network Errors

### **ReadTimeout Error**
```
requests.exceptions.ReadTimeout: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)
```

**What it means**: The bot couldn't get a response from Telegram's servers within 30 seconds.

**Causes**:
- Slow internet connection
- Telegram API temporary issues
- Network congestion
- Firewall blocking connections

## ✅ Solutions Implemented

### **1. Automatic Retry Logic**
The bot now automatically retries failed requests:
- **3 attempts** for each message
- **Exponential backoff** (2, 4, 8 seconds)
- **Graceful fallback** if all attempts fail

### **2. Safe Message Functions**
All bot messages now use safe sending functions:
```python
safe_send_message(chat_id, text)  # Instead of bot.send_message()
safe_edit_message(chat_id, message_id, text)  # Instead of bot.edit_message_text()
```

### **3. Robust Polling Loop**
The bot automatically restarts on network errors:
- **Catches timeout errors**
- **Waits 5 seconds** before retry
- **Continues running** without crashing

## 🚀 How to Start the Bot

### **Option 1: Use Robust Startup Script (Recommended)**
```bash
python start_teacher_bot.py
```

**Benefits**:
- ✅ Checks internet connection first
- ✅ Displays teacher credentials
- ✅ Automatic restart on crashes
- ✅ Better error messages

### **Option 2: Direct Start**
```bash
python TelegramBot.py
```

**What you'll see**:
```
🤖 Starting Teacher Bot with Multi-Teacher Authentication...
🔐 Three teachers can login with their unique passwords
📋 Each teacher has their own isolated workspace
⚠️ Network timeout protection enabled

Press Ctrl+C to stop the bot...
🚀 Bot is running...
```

## 🔧 Troubleshooting Steps

### **If Bot Keeps Timing Out:**

1. **Check Internet Connection**
   ```bash
   ping google.com
   ```

2. **Test Telegram API Access**
   ```bash
   curl -I https://api.telegram.org
   ```

3. **Check Firewall Settings**
   - Ensure port 443 (HTTPS) is open
   - Allow Python.exe through firewall

4. **Try Different Network**
   - Switch to mobile hotspot
   - Try different WiFi network

### **If Bot Crashes Repeatedly:**

1. **Check Bot Token**
   - Verify token is correct in TelegramBot.py
   - Test token with simple API call

2. **Check Database Connection**
   ```bash
   python test_teacher_system.py
   ```

3. **Update Dependencies**
   ```bash
   pip install --upgrade pyTelegramBotAPI requests
   ```

### **If Login Doesn't Work:**

1. **Verify Passwords**
   - Copy-paste exactly (case sensitive)
   - Check for extra spaces

2. **Check Authentication System**
   ```bash
   python -c "from db import authenticate_teacher; print(authenticate_teacher('&e^!tK$mVKwLyGjQXq6RTUsJ#@X$n@ycvjU6E!a^EDLfslFx$A'))"
   ```

## 📊 Error Messages Explained

### **"⚠️ Network timeout on attempt X, retrying..."**
- **Normal**: Temporary network issue
- **Action**: Wait, bot will retry automatically

### **"❌ Failed to send message after 3 attempts"**
- **Issue**: Persistent network problem
- **Action**: Check internet connection

### **"🔄 Retrying in 5 seconds..."**
- **Normal**: Bot lost connection to Telegram
- **Action**: Wait, bot will reconnect automatically

### **"❌ Unexpected error: ..."**
- **Issue**: Non-network error occurred
- **Action**: Check error details, may need code fix

## 🛡️ Prevention Tips

### **Stable Network Environment**
- Use wired connection when possible
- Avoid peak internet usage times
- Consider VPN if Telegram is restricted

### **Bot Configuration**
- Keep bot running on stable server
- Use process manager (PM2, systemd)
- Monitor bot health regularly

### **Backup Plans**
- Have multiple network options
- Keep bot credentials secure
- Regular database backups

## 🔍 Monitoring Bot Health

### **Check if Bot is Responsive**
```bash
# Send test message to your bot
# Should get response within 30 seconds
```

### **Monitor Network Issues**
```bash
# Check bot logs for timeout patterns
# Look for repeated retry messages
```

### **Database Health Check**
```bash
python test_teacher_system.py
```

## 📞 When to Seek Help

### **Contact Support If:**
- Bot crashes immediately on start
- Authentication system completely broken
- Database connection fails
- Persistent errors after following all steps

### **Provide This Information:**
- Full error message
- Network environment details
- Steps that led to the error
- Bot logs from startup

## 🎯 Success Indicators

### **Bot is Working Correctly When:**
✅ **Starts without errors**
✅ **Responds to /login command**
✅ **Teachers can authenticate**
✅ **Commands work normally**
✅ **Handles network timeouts gracefully**
✅ **Automatically recovers from disconnections**

The network error handling system makes the bot much more robust and reliable! 🚀
