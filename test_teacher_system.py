#!/usr/bin/env python3
"""
Test script for the multi-teacher authentication system
"""

import sys

def test_database_columns():
    """Test if teacher_id columns exist"""
    print("🔍 Testing database columns...")
    
    try:
        from db import supabase
        
        # Test batches_metadata table
        try:
            result = supabase.table("batches_metadata").select("teacher_id").limit(1).execute()
            print("✅ batches_metadata.teacher_id column exists")
        except Exception as e:
            if "does not exist" in str(e):
                print("❌ batches_metadata.teacher_id column missing")
                return False
            else:
                print(f"⚠️ batches_metadata test error: {e}")
        
        # Test questions table
        try:
            result = supabase.table("questions").select("teacher_id").limit(1).execute()
            print("✅ questions.teacher_id column exists")
        except Exception as e:
            if "does not exist" in str(e):
                print("❌ questions.teacher_id column missing")
                return False
            else:
                print(f"⚠️ questions test error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def test_authentication():
    """Test teacher authentication"""
    print("\n🔐 Testing authentication system...")
    
    try:
        from db import authenticate_teacher, TEACHER_PASSWORDS
        
        # Test each teacher password
        for i, (teacher_id, password) in enumerate(TEACHER_PASSWORDS.items(), 1):
            result = authenticate_teacher(password)
            if result == teacher_id:
                print(f"✅ Teacher {i} ({teacher_id}) authentication: PASS")
            else:
                print(f"❌ Teacher {i} ({teacher_id}) authentication: FAIL")
                return False
        
        # Test invalid password
        invalid_result = authenticate_teacher("wrong_password")
        if invalid_result is None:
            print("✅ Invalid password rejection: PASS")
        else:
            print("❌ Invalid password rejection: FAIL")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication test error: {e}")
        return False

def test_teacher_functions():
    """Test teacher-specific database functions"""
    print("\n📊 Testing teacher-specific functions...")
    
    try:
        from db import get_teacher_batches, get_teacher_subjects, get_questions_by_subject_and_teacher
        
        # Test get_teacher_batches
        try:
            batches = get_teacher_batches("teacher1")
            print(f"✅ get_teacher_batches: Found {len(batches)} batches for teacher1")
        except Exception as e:
            print(f"❌ get_teacher_batches error: {e}")
            return False
        
        # Test get_teacher_subjects
        try:
            subjects = get_teacher_subjects("teacher1")
            print(f"✅ get_teacher_subjects: Found {len(subjects)} subjects for teacher1")
        except Exception as e:
            print(f"❌ get_teacher_subjects error: {e}")
            return False
        
        # Test get_questions_by_subject_and_teacher
        if subjects:
            try:
                questions = get_questions_by_subject_and_teacher(subjects[0], "teacher1")
                print(f"✅ get_questions_by_subject_and_teacher: Found {len(questions)} questions")
            except Exception as e:
                print(f"❌ get_questions_by_subject_and_teacher error: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Teacher functions test error: {e}")
        return False

def test_bot_imports():
    """Test if bot imports work"""
    print("\n🤖 Testing bot imports...")
    
    try:
        # Test importing TelegramBot without running it
        import importlib.util
        spec = importlib.util.spec_from_file_location("TelegramBot", "TelegramBot.py")
        if spec is None:
            print("❌ Cannot find TelegramBot.py")
            return False
        
        # Just test if it can be imported (syntax check)
        print("✅ TelegramBot.py syntax is valid")
        return True
        
    except Exception as e:
        print(f"❌ Bot import error: {e}")
        return False

def show_setup_status():
    """Show current setup status"""
    print("\n📋 Current Setup Status:")
    print("=" * 50)
    
    try:
        from db import supabase
        
        # Check batches with teacher_id
        try:
            batches_resp = supabase.table("batches_metadata").select("teacher_id, subject").execute()
            if batches_resp.data:
                teacher_counts = {}
                for batch in batches_resp.data:
                    teacher_id = batch.get('teacher_id', 'unassigned')
                    teacher_counts[teacher_id] = teacher_counts.get(teacher_id, 0) + 1
                
                print("📋 Batches by teacher:")
                for teacher_id, count in teacher_counts.items():
                    print(f"  {teacher_id}: {count} batches")
            else:
                print("📋 No batches found")
        except Exception as e:
            print(f"📋 Batches check failed: {e}")
        
        # Check questions with teacher_id
        try:
            questions_resp = supabase.table("questions").select("teacher_id").execute()
            if questions_resp.data:
                teacher_counts = {}
                for question in questions_resp.data:
                    teacher_id = question.get('teacher_id', 'unassigned')
                    teacher_counts[teacher_id] = teacher_counts.get(teacher_id, 0) + 1
                
                print("📝 Questions by teacher:")
                for teacher_id, count in teacher_counts.items():
                    print(f"  {teacher_id}: {count} questions")
            else:
                print("📝 No questions found")
        except Exception as e:
            print(f"📝 Questions check failed: {e}")
            
    except Exception as e:
        print(f"❌ Status check error: {e}")

def main():
    """Run all tests"""
    print("🧪 Multi-Teacher System Test")
    print("=" * 40)
    
    tests = [
        ("Database Columns", test_database_columns),
        ("Authentication", test_authentication),
        ("Teacher Functions", test_teacher_functions),
        ("Bot Imports", test_bot_imports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    # Show current status
    show_setup_status()
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Multi-teacher system is ready.")
        print("\n🚀 You can now:")
        print("   • Start the bot: python TelegramBot.py")
        print("   • Login with teacher passwords")
        print("   • Create separate quizzes for each teacher")
    else:
        print("⚠️ Some tests failed. Setup may be incomplete.")
        print("\n🔧 Next steps:")
        if passed == 0:
            print("   • Follow manual_setup_guide.md")
            print("   • Add teacher_id columns to database")
        else:
            print("   • Check the failed tests above")
            print("   • Verify database permissions")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
