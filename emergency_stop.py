#!/usr/bin/env python3
"""
Emergency script to stop all bot conflicts
"""

import subprocess
import sys
import time
import os

def force_kill_python():
    """Force kill all Python processes"""
    print("🚨 EMERGENCY STOP - Killing all Python processes...")
    
    try:
        # Windows
        if os.name == 'nt':
            subprocess.run(['taskkill', '/f', '/im', 'python.exe'], 
                         capture_output=True, text=True)
            subprocess.run(['taskkill', '/f', '/im', 'pythonw.exe'], 
                         capture_output=True, text=True)
            print("✅ Killed all python.exe and pythonw.exe processes")
        else:
            # Linux/Mac
            subprocess.run(['pkill', '-f', 'python'], capture_output=True)
            print("✅ Killed all Python processes")
            
    except Exception as e:
        print(f"⚠️ Error killing processes: {e}")
        print("🔧 Please manually kill Python processes in Task Manager")

def wait_for_telegram():
    """Wait for Telegram to clear connections"""
    print("\n⏳ Waiting 60 seconds for Telegram to clear connections...")
    
    for i in range(60, 0, -1):
        print(f"\r⏰ {i} seconds remaining...", end='', flush=True)
        time.sleep(1)
    
    print("\n✅ Wait complete")

def check_remaining_processes():
    """Check if any Python processes are still running"""
    print("\n🔍 Checking for remaining Python processes...")
    
    try:
        if os.name == 'nt':
            # Windows
            result = subprocess.run(['tasklist', '/fi', 'imagename eq python.exe'], 
                                  capture_output=True, text=True)
            if 'python.exe' in result.stdout:
                print("⚠️ Some Python processes are still running:")
                print(result.stdout)
                return False
            else:
                print("✅ No Python processes found")
                return True
        else:
            # Linux/Mac
            result = subprocess.run(['pgrep', '-f', 'python'], 
                                  capture_output=True, text=True)
            if result.stdout.strip():
                print("⚠️ Some Python processes are still running:")
                print(result.stdout)
                return False
            else:
                print("✅ No Python processes found")
                return True
                
    except Exception as e:
        print(f"⚠️ Could not check processes: {e}")
        return True  # Assume it's OK

def main():
    """Emergency stop procedure"""
    print("🚨 EMERGENCY BOT CONFLICT RESOLUTION")
    print("=" * 50)
    
    # Step 1: Force kill everything
    force_kill_python()
    
    # Step 2: Wait for Telegram
    wait_for_telegram()
    
    # Step 3: Check results
    if check_remaining_processes():
        print("\n🎉 SUCCESS! All conflicts resolved")
        print("\n🚀 You can now start bots safely:")
        print("   python TelegramBot.py")
        print("   python StudentBot.py")
        print("\n⚠️ Start them in SEPARATE terminals")
        print("⚠️ Do NOT use run_bots.py or start_both_bots.py yet")
    else:
        print("\n❌ Some processes are still running")
        print("🔧 Please:")
        print("   1. Restart your computer")
        print("   2. Or manually kill processes in Task Manager")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Emergency stop cancelled")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("🔧 Please restart your computer to clear all processes")
