# 👥 Multi-Teacher Authentication System

## 🎯 Overview

The Telegram quiz bot now supports **three separate teachers** with individual authentication, isolated sessions, and private data access. Each teacher has their own password and can only access their own quizzes and questions.

## 🔐 Teacher Credentials

### **Teacher 1**
- **Password**: `&e^!tK$mVKwLyGjQXq6RTUsJ#@X$n@ycvjU6E!a^EDLfslFx$A`
- **Display Name**: المعلم الأول

### **Teacher 2** 
- **Password**: `yd2gI4&h3Qs3USd8n63@zsGbI!xA3#tWVnIn2Fq9xouLRkHnch`
- **Display Name**: المعلم الثاني

### **Teacher 3**
- **Password**: `5!MM8MA!BLcqDaPC3%4rgB7kPsxF376HJ$6ncl43#!EMqE6*zx`
- **Display Name**: المعلم الثالث

## 🚀 How to Use

### **Step 1: Login**
```
Teacher sends: /login
Bot: 🔐 مرحباً بك في نظام إدارة الاختبارات للمعلمين

🔑 يرجى إدخال كلمة المرور الخاصة بك:

Teacher types: [their password]
Bot: ✅ تم تسجيل الدخول بنجاح!
🎓 مرحباً المعلم الأول
```

### **Step 2: Use Commands**
After login, all commands work normally:
- `/insertQuestions` - Add new quizzes
- `/Questions` - View your quizzes only
- `/EditQuestion` - Edit your questions only
- `/test` - Test functionality
- `/logout` - End session

### **Step 3: Logout**
```
Teacher sends: /logout
Bot: 👋 تم تسجيل خروج المعلم الأول بنجاح
استخدم /login لتسجيل الدخول مرة أخرى
```

## 🛡️ Security Features

### **🔐 Password Protection**
- **Static passwords** for each teacher
- **3 login attempts** maximum
- **Session timeout** protection
- **Secure password validation**

### **🏠 Data Isolation**
- **Separate quiz collections** for each teacher
- **Private question banks** - no cross-access
- **Individual subject lists** per teacher
- **Isolated editing permissions**

### **👤 Session Management**
- **One session per chat** - prevents conflicts
- **Automatic logout** on bot restart
- **Session verification** for all commands
- **Teacher identification** in all operations

## 📋 Command Changes

### **Before Authentication Required:**
- `/start` - Welcome message with login instructions
- `/login` - Start authentication process

### **After Authentication Required:**
- `/insertQuestions` - ✅ Requires login
- `/Questions` - ✅ Requires login (shows only your quizzes)
- `/EditQuestion` - ✅ Requires login (edit only your questions)
- `/test` - ✅ Requires login
- `/logout` - End your session

## 🔍 What Each Teacher Sees

### **Teacher 1 Experience:**
```
/Questions shows:
📋 اختبارات المعلم الأول:

📚 Mathematics - 5 أسئلة - المدة: 10 دقائق
🔗 https://t.me/TestStudentCollegeBot?start=quiz_abc123

📚 Science - 3 أسئلة - المدة: 15 دقائق  
🔗 https://t.me/TestStudentCollegeBot?start=quiz_def456

📊 إجمالي الاختبارات: 2
```

### **Teacher 2 Experience:**
```
/Questions shows:
📋 اختبارات المعلم الثاني:

📚 English - 4 أسئلة - المدة: 20 دقائق
🔗 https://t.me/TestStudentCollegeBot?start=quiz_ghi789

📊 إجمالي الاختبارات: 1
```

## 🛠️ Technical Implementation

### **Database Schema Updates:**
```sql
-- Added to batches_metadata
teacher_id TEXT

-- Added to questions  
teacher_id TEXT
```

### **Authentication Flow:**
1. **Teacher sends** `/login`
2. **Bot requests** password
3. **Teacher enters** password
4. **System validates** against static passwords
5. **Session created** with teacher_id
6. **All commands** check session validity

### **Data Filtering:**
```python
# Example: Get only teacher's batches
def get_teacher_batches(teacher_id):
    return supabase.table("batches_metadata").select("*").eq("teacher_id", teacher_id).execute()

# Example: Get only teacher's questions
def get_questions_by_subject_and_teacher(subject, teacher_id):
    return supabase.table("questions").select("*").eq("subject", subject).eq("teacher_id", teacher_id).execute()
```

## 🚨 Error Handling

### **Authentication Errors:**
- **Wrong password**: 3 attempts allowed
- **Too many attempts**: Temporary lockout
- **Session expired**: Automatic logout required

### **Authorization Errors:**
- **Accessing other teacher's data**: Blocked with error message
- **Unauthenticated access**: Redirected to login
- **Invalid session**: Forced re-authentication

## 📊 Setup Instructions

### **1. Run Setup Script:**
```bash
python setup_teacher_system.py
```

### **2. Verify Database:**
- Check teacher_id columns added
- Verify existing data assigned to teacher1
- Test authentication system

### **3. Test Each Teacher:**
- Login with each password
- Create test quizzes
- Verify data isolation

## 🎯 Benefits

### **For Teachers:**
✅ **Private workspace** - Only see your own content
✅ **Secure access** - Password-protected sessions  
✅ **Independent operation** - No interference between teachers
✅ **Familiar interface** - Same commands, just authenticated

### **For Administration:**
✅ **User management** - Track which teacher created what
✅ **Data organization** - Clear separation of content
✅ **Security compliance** - Protected access to sensitive data
✅ **Scalable system** - Easy to add more teachers

## 🔧 Troubleshooting

### **"يجب تسجيل الدخول أولاً"**
- **Cause**: Not authenticated
- **Solution**: Use `/login` command

### **"كلمة مرور خاطئة"**
- **Cause**: Incorrect password
- **Solution**: Check password carefully (case-sensitive)

### **"تم تجاوز عدد المحاولات"**
- **Cause**: 3 failed login attempts
- **Solution**: Wait and try `/login` again

### **"غير مصرح لك بالوصول"**
- **Cause**: Trying to access another teacher's data
- **Solution**: Only access your own content

## 🚀 Migration Notes

### **Existing Data:**
- All existing quizzes assigned to **teacher1**
- Teacher1 can access all historical data
- New quizzes are teacher-specific

### **Student Experience:**
- **No changes** for students
- Quiz links work exactly the same
- Same @TestStudentCollegeBot interface

## 🎉 Ready to Use!

The multi-teacher system is now active. Each teacher can:

1. **Login** with their unique password
2. **Create** private quizzes and questions  
3. **Manage** only their own content
4. **Generate** quiz links for students
5. **Edit** their questions securely

All while maintaining complete data isolation and security! 🛡️
