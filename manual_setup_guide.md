# 🛠️ Manual Database Setup for Multi-Teacher System

## ⚠️ Required Database Changes

The multi-teacher system requires adding `teacher_id` columns to existing tables. Since automatic setup failed, please follow these manual steps:

## 📋 Step 1: Add Columns to Database

### **Option A: Using Supabase Dashboard (Recommended)**

1. **Go to your Supabase project dashboard**
2. **Navigate to**: Table Editor
3. **Select `batches_metadata` table**
4. **Click**: "Add Column" button
5. **Add new column**:
   - **Name**: `teacher_id`
   - **Type**: `text`
   - **Default**: `NULL`
   - **Nullable**: ✅ Yes
6. **Click**: "Save"

7. **Select `questions` table**
8. **Click**: "Add Column" button  
9. **Add new column**:
   - **Name**: `teacher_id`
   - **Type**: `text`
   - **Default**: `NULL`
   - **Nullable**: ✅ Yes
10. **Click**: "Save"

### **Option B: Using SQL Editor**

1. **Go to**: SQL Editor in Supabase dashboard
2. **Run these commands one by one**:

```sql
-- Add teacher_id to batches_metadata
ALTER TABLE batches_metadata ADD COLUMN IF NOT EXISTS teacher_id TEXT;

-- Add teacher_id to questions  
ALTER TABLE questions ADD COLUMN IF NOT EXISTS teacher_id TEXT;

-- Assign existing data to teacher1
UPDATE batches_metadata SET teacher_id = 'teacher1' WHERE teacher_id IS NULL;
UPDATE questions SET teacher_id = 'teacher1' WHERE teacher_id IS NULL;
```

## 📋 Step 2: Verify Changes

### **Check Tables Structure:**

1. **Go to**: Table Editor
2. **Check `batches_metadata`** - should have `teacher_id` column
3. **Check `questions`** - should have `teacher_id` column

### **Check Data Assignment:**

1. **View `batches_metadata` data** - all rows should have `teacher_id = 'teacher1'`
2. **View `questions` data** - all rows should have `teacher_id = 'teacher1'`

## 🚀 Step 3: Test the System

### **Run the Test Script:**
```bash
python test_teacher_system.py
```

### **Or Test Manually:**

1. **Start the bot**:
   ```bash
   python TelegramBot.py
   ```

2. **Test Teacher 1 Login**:
   ```
   /login
   &e^!tK$mVKwLyGjQXq6RTUsJ#@X$n@ycvjU6E!a^EDLfslFx$A
   ```

3. **Test Commands**:
   ```
   /Questions
   /insertQuestions
   /EditQuestion
   ```

## 🔧 Troubleshooting

### **Error: "column does not exist"**
- **Cause**: Columns not added yet
- **Solution**: Complete Step 1 above

### **Error: "لا توجد اختبارات مضافة"**
- **Cause**: No data assigned to teacher
- **Solution**: Run the UPDATE commands from Step 1

### **Error: "يجب تسجيل الدخول أولاً"**
- **Cause**: Not authenticated
- **Solution**: Use `/login` command first

## ✅ Expected Results

### **After Successful Setup:**

1. **Teacher 1 can login** with their password
2. **Teacher 1 sees existing quizzes** (all historical data)
3. **Teachers 2 & 3 can login** but see no quizzes initially
4. **Each teacher can create** new quizzes independently
5. **Data isolation works** - teachers only see their own content

### **Database Structure:**
```
batches_metadata:
- batch_id (existing)
- subject (existing)  
- number_of_questions (existing)
- duration_minutes (existing)
- teacher_id (NEW)

questions:
- id (existing)
- question (existing)
- options (existing)
- answer (existing)
- batch_id (existing)
- link_token (existing)
- used (existing)
- subject (existing)
- teacher_id (NEW)
```

## 🎯 Quick Verification Commands

### **SQL Queries to Check Setup:**

```sql
-- Check if columns exist
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'batches_metadata' AND column_name = 'teacher_id';

SELECT column_name FROM information_schema.columns 
WHERE table_name = 'questions' AND column_name = 'teacher_id';

-- Check data assignment
SELECT teacher_id, COUNT(*) FROM batches_metadata GROUP BY teacher_id;
SELECT teacher_id, COUNT(*) FROM questions GROUP BY teacher_id;
```

### **Expected Results:**
```
-- Columns should exist
teacher_id

-- Data should be assigned
teacher1 | [number of existing batches]
teacher1 | [number of existing questions]
```

## 🚨 If Setup Still Fails

### **Contact Information:**
If you continue having issues, please:

1. **Check Supabase permissions** - ensure you have table modification rights
2. **Verify database connection** - test with simple queries
3. **Check for table locks** - ensure no other processes are using tables
4. **Try browser refresh** - sometimes Supabase dashboard needs refresh

### **Alternative Approach:**
If columns cannot be added, we can modify the code to work without teacher_id columns by using a different identification method.

## 🎉 Success Indicators

You'll know setup is complete when:

✅ **Columns exist** in both tables
✅ **Existing data assigned** to teacher1  
✅ **Bot starts** without errors
✅ **Login works** for all three teachers
✅ **Commands require** authentication
✅ **Data isolation** functions properly

Once setup is complete, the multi-teacher system will be fully operational! 🚀
