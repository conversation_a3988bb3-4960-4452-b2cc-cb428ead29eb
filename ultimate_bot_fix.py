#!/usr/bin/env python3
"""
Ultimate bot conflict resolution - fixes webhook conflicts and other issues
"""

import requests
import time
import sys
import subprocess
import os

# Bot tokens
TEACHER_TOKEN = "7432401952:AAHm4Sez4z8_zzwmo3A_k2eY-gcYDn8j0Z8"
STUDENT_TOKEN = "8042475385:AAGipEjvwZrxlibKGRtcoQFGYEYJWb9amBE"

def check_bot_info(token, bot_name):
    """Check bot information and status"""
    print(f"\n🔍 Checking {bot_name}...")
    
    try:
        # Get bot info
        response = requests.get(f"https://api.telegram.org/bot{token}/getMe", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data['ok']:
                bot_info = data['result']
                print(f"✅ Bot: @{bot_info['username']} ({bot_info['first_name']})")
                return True
            else:
                print(f"❌ Bot API error: {data.get('description', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def delete_webhook(token, bot_name):
    """Delete webhook if set (this causes conflicts with polling)"""
    print(f"\n🔧 Removing webhook for {bot_name}...")
    
    try:
        # First check if webhook is set
        response = requests.get(f"https://api.telegram.org/bot{token}/getWebhookInfo", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data['ok']:
                webhook_info = data['result']
                if webhook_info.get('url'):
                    print(f"⚠️ Webhook found: {webhook_info['url']}")
                    print("🔧 This causes conflicts with polling - removing...")
                    
                    # Delete webhook
                    delete_response = requests.post(
                        f"https://api.telegram.org/bot{token}/deleteWebhook",
                        json={"drop_pending_updates": True},
                        timeout=10
                    )
                    
                    if delete_response.status_code == 200:
                        delete_data = delete_response.json()
                        if delete_data['ok']:
                            print("✅ Webhook deleted successfully")
                            return True
                        else:
                            print(f"❌ Failed to delete webhook: {delete_data.get('description')}")
                            return False
                    else:
                        print(f"❌ HTTP error deleting webhook: {delete_response.status_code}")
                        return False
                else:
                    print("✅ No webhook set")
                    return True
            else:
                print(f"❌ Error checking webhook: {data.get('description')}")
                return False
        else:
            print(f"❌ HTTP error checking webhook: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error with webhook: {e}")
        return False

def clear_pending_updates(token, bot_name):
    """Clear any pending updates that might cause conflicts"""
    print(f"\n🧹 Clearing pending updates for {bot_name}...")
    
    try:
        # Get updates with high offset to clear them
        response = requests.post(
            f"https://api.telegram.org/bot{token}/getUpdates",
            json={"offset": -1, "timeout": 1},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['ok']:
                updates = data['result']
                if updates:
                    # Get the highest update_id and clear all
                    highest_id = max(update['update_id'] for update in updates)
                    clear_response = requests.post(
                        f"https://api.telegram.org/bot{token}/getUpdates",
                        json={"offset": highest_id + 1, "timeout": 1},
                        timeout=10
                    )
                    print(f"✅ Cleared {len(updates)} pending updates")
                else:
                    print("✅ No pending updates")
                return True
            else:
                print(f"❌ Error getting updates: {data.get('description')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error clearing updates: {e}")
        return False

def force_kill_processes():
    """Force kill all Python processes"""
    print("\n🛑 Force killing all Python processes...")
    
    try:
        if os.name == 'nt':  # Windows
            subprocess.run(['taskkill', '/f', '/im', 'python.exe'], 
                         capture_output=True, text=True)
            subprocess.run(['taskkill', '/f', '/im', 'pythonw.exe'], 
                         capture_output=True, text=True)
            print("✅ Killed all Python processes")
        else:  # Linux/Mac
            subprocess.run(['pkill', '-f', 'python'], capture_output=True)
            print("✅ Killed all Python processes")
            
    except Exception as e:
        print(f"⚠️ Error killing processes: {e}")

def test_bot_connection(token, bot_name):
    """Test if bot can connect without conflicts"""
    print(f"\n🧪 Testing {bot_name} connection...")
    
    try:
        # Try to get updates (this is what polling does)
        response = requests.post(
            f"https://api.telegram.org/bot{token}/getUpdates",
            json={"timeout": 1, "limit": 1},
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['ok']:
                print(f"✅ {bot_name} can connect successfully")
                return True
            else:
                error_desc = data.get('description', 'Unknown error')
                if 'conflict' in error_desc.lower():
                    print(f"❌ {bot_name} has conflict: {error_desc}")
                    return False
                else:
                    print(f"⚠️ {bot_name} error: {error_desc}")
                    return False
        else:
            print(f"❌ {bot_name} HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ {bot_name} connection error: {e}")
        return False

def main():
    """Ultimate bot fix procedure"""
    print("🚨 ULTIMATE BOT CONFLICT RESOLUTION")
    print("=" * 60)
    print("This will fix webhook conflicts and other issues")
    print("=" * 60)
    
    success = True
    
    # Step 1: Force kill processes
    force_kill_processes()
    
    # Step 2: Check both bots
    print("\n📋 STEP 1: Checking bot status...")
    teacher_ok = check_bot_info(TEACHER_TOKEN, "Teacher Bot")
    student_ok = check_bot_info(STUDENT_TOKEN, "Student Bot")
    
    if not teacher_ok or not student_ok:
        print("\n❌ One or both bots have issues with their tokens")
        print("🔧 Please check your bot tokens with @BotFather")
        return 1
    
    # Step 3: Remove webhooks (major cause of conflicts)
    print("\n📋 STEP 2: Removing webhooks...")
    teacher_webhook_ok = delete_webhook(TEACHER_TOKEN, "Teacher Bot")
    student_webhook_ok = delete_webhook(STUDENT_TOKEN, "Student Bot")
    
    if not teacher_webhook_ok or not student_webhook_ok:
        success = False
    
    # Step 4: Clear pending updates
    print("\n📋 STEP 3: Clearing pending updates...")
    teacher_clear_ok = clear_pending_updates(TEACHER_TOKEN, "Teacher Bot")
    student_clear_ok = clear_pending_updates(STUDENT_TOKEN, "Student Bot")
    
    if not teacher_clear_ok or not student_clear_ok:
        success = False
    
    # Step 5: Wait for Telegram to process changes
    print("\n📋 STEP 4: Waiting for Telegram to process changes...")
    for i in range(30, 0, -1):
        print(f"\r⏰ Waiting {i} seconds...", end='', flush=True)
        time.sleep(1)
    print()
    
    # Step 6: Test connections
    print("\n📋 STEP 5: Testing bot connections...")
    teacher_test_ok = test_bot_connection(TEACHER_TOKEN, "Teacher Bot")
    student_test_ok = test_bot_connection(STUDENT_TOKEN, "Student Bot")
    
    # Final result
    print("\n" + "=" * 60)
    if teacher_test_ok and student_test_ok:
        print("🎉 SUCCESS! Both bots are ready to use")
        print("\n🚀 You can now start the bots:")
        print("   Terminal 1: python TelegramBot.py")
        print("   Terminal 2: python StudentBot.py")
        print("\n⚠️ Start them in SEPARATE terminal windows")
        print("⚠️ Do NOT use combined startup scripts yet")
        return 0
    else:
        print("❌ FAILED! Some issues remain")
        if not teacher_test_ok:
            print("   • Teacher Bot has conflicts")
        if not student_test_ok:
            print("   • Student Bot has conflicts")
        
        print("\n🔧 Additional steps to try:")
        print("   1. Contact @BotFather to revoke and recreate bot tokens")
        print("   2. Check if bots are running on another computer/server")
        print("   3. Wait 24 hours for Telegram to clear all conflicts")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Fix cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
